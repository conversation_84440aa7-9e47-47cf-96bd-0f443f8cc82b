"""
认证相关API接口
包括登录、注册、令牌刷新、设备授权等
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr

from app.core.database import get_db
from app.core.security import (
    verify_password, 
    get_password_hash, 
    create_tokens,
    verify_token,
    generate_device_code,
    Token
)
from app.core.dependencies import get_current_user, get_redis
from app.models.user import User
from app.schemas.user import UserCreate, UserResponse
from app.services.auth_service import AuthService
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class LoginRequest(BaseModel):
    """登录请求"""
    username: str
    password: str


class RegisterRequest(BaseModel):
    """注册请求"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None


class DeviceAuthRequest(BaseModel):
    """设备授权请求"""
    device_name: str
    device_type: str = "browser_extension"


class DeviceAuthResponse(BaseModel):
    """设备授权响应"""
    device_code: str
    user_code: str
    verification_uri: str
    expires_in: int
    interval: int


class TokenRefreshRequest(BaseModel):
    """令牌刷新请求"""
    refresh_token: str


@router.post("/login", response_model=Token)
async def login(
    request: LoginRequest,
    db: Session = Depends(get_db),
    auth_service: AuthService = Depends()
):
    """用户登录"""
    try:
        user = db.query(User).filter(
            (User.username == request.username) | 
            (User.email == request.username)
        ).first()
        
        if not user or not verify_password(request.password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.commit()
        
        # 创建令牌
        scopes = ["read", "write"]
        if user.is_admin:
            scopes.append("admin")
            
        tokens = create_tokens(str(user.id), user.username, scopes)
        
        logger.info(f"User {user.username} logged in successfully")
        return tokens
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/register", response_model=UserResponse)
async def register(
    request: RegisterRequest,
    db: Session = Depends(get_db)
):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        if db.query(User).filter(User.username == request.username).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )
        
        # 检查邮箱是否已存在
        if db.query(User).filter(User.email == request.email).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # 创建新用户
        user = User(
            username=request.username,
            email=request.email,
            full_name=request.full_name,
            hashed_password=get_password_hash(request.password),
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        logger.info(f"New user registered: {user.username}")
        return UserResponse.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/device/authorize", response_model=DeviceAuthResponse)
async def device_authorize(
    request: DeviceAuthRequest,
    redis = Depends(get_redis)
):
    """设备授权 - 用于浏览器插件认证"""
    try:
        user_code, device_code = generate_device_code()
        
        # 存储设备授权信息到Redis
        device_data = {
            "device_name": request.device_name,
            "device_type": request.device_type,
            "user_code": user_code,
            "status": "pending",
            "created_at": datetime.utcnow().isoformat()
        }
        
        # 设置过期时间为5分钟
        await redis.setex(
            f"device_auth:{device_code}", 
            300, 
            str(device_data)
        )
        
        await redis.setex(
            f"user_code:{user_code}", 
            300, 
            device_code
        )
        
        return DeviceAuthResponse(
            device_code=device_code,
            user_code=user_code,
            verification_uri="/auth/device/verify",
            expires_in=300,
            interval=5
        )
        
    except Exception as e:
        logger.error(f"Device authorization error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Device authorization failed"
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    request: TokenRefreshRequest,
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        token_data = verify_token(request.refresh_token)
        
        user = db.query(User).filter(User.id == token_data.user_id).first()
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # 创建新的令牌对
        scopes = ["read", "write"]
        if user.is_admin:
            scopes.append("admin")
            
        tokens = create_tokens(str(user.id), user.username, scopes)
        
        return tokens
        
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token refresh failed"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """获取当前用户信息"""
    return UserResponse.from_orm(current_user)


@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    redis = Depends(get_redis)
):
    """用户登出"""
    try:
        # 可以在这里实现令牌黑名单机制
        # 目前简单返回成功
        logger.info(f"User {current_user.username} logged out")
        return {"message": "Successfully logged out"}
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )
