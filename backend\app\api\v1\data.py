"""
数据处理API接口
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime

from app.core.database import get_db
from app.core.dependencies import get_current_user, upload_rate_limit
from app.models.user import User
from app.schemas.data import DataResponse, DataQuery, DataUploadResponse
from app.services.data_processor import DataProcessorService
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class DataListResponse(BaseModel):
    """数据列表响应"""
    data: List[DataResponse]
    total: int
    page: int
    size: int


@router.post("/upload", response_model=DataUploadResponse)
async def upload_data(
    file: UploadFile = File(...),
    task_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(upload_rate_limit)
):
    """上传数据文件"""
    try:
        data_processor = DataProcessorService(db)
        result = await data_processor.upload_file(
            file, 
            str(current_user.id), 
            task_id
        )
        
        logger.info(f"File uploaded: {file.filename} by user {current_user.username}")
        return result
        
    except Exception as e:
        logger.error(f"Upload data error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload data"
        )


@router.get("/", response_model=DataListResponse)
async def get_data(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    task_id: Optional[str] = Query(None),
    data_type: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取数据列表"""
    try:
        data_processor = DataProcessorService(db)
        result = await data_processor.get_user_data(
            str(current_user.id),
            page=page,
            size=size,
            task_id=task_id,
            data_type=data_type
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Get data error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get data"
        )


@router.get("/{data_id}", response_model=DataResponse)
async def get_data_detail(
    data_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取数据详情"""
    try:
        data_processor = DataProcessorService(db)
        result = await data_processor.get_data_detail(
            data_id, 
            str(current_user.id)
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Get data detail error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get data detail"
        )


@router.post("/query", response_model=DataListResponse)
async def query_data(
    query: DataQuery,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """查询数据"""
    try:
        data_processor = DataProcessorService(db)
        result = await data_processor.query_data(
            query, 
            str(current_user.id)
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Query data error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to query data"
        )


@router.delete("/{data_id}")
async def delete_data(
    data_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除数据"""
    try:
        data_processor = DataProcessorService(db)
        await data_processor.delete_data(
            data_id, 
            str(current_user.id)
        )
        
        logger.info(f"Data deleted: {data_id}")
        return {"message": "Data deleted successfully"}
        
    except Exception as e:
        logger.error(f"Delete data error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete data"
        )
