"""
依赖注入模块
提供FastAPI依赖注入函数
"""

from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import security, verify_token, TokenData
from app.models.user import User
from app.utils.redis_client import get_redis_client
from app.utils.logger import get_logger

logger = get_logger(__name__)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前认证用户"""
    token = credentials.credentials
    token_data = verify_token(token)
    
    user = db.query(User).filter(User.id == token_data.user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前管理员用户"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


def require_scopes(required_scopes: list[str]):
    """权限范围检查装饰器"""
    def scope_checker(
        credentials: HTTPAuthorizationCredentials = Depends(security)
    ) -> TokenData:
        token = credentials.credentials
        token_data = verify_token(token)
        
        for scope in required_scopes:
            if scope not in token_data.scopes:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Not enough permissions. Required scope: {scope}"
                )
        
        return token_data
    
    return scope_checker


async def get_redis():
    """获取Redis客户端"""
    return await get_redis_client()


class RateLimiter:
    """API限流器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
    
    async def __call__(
        self,
        user: User = Depends(get_current_user),
        redis = Depends(get_redis)
    ):
        """检查用户请求频率"""
        key = f"rate_limit:{user.id}"
        
        try:
            current_requests = await redis.get(key)
            if current_requests is None:
                await redis.setex(key, self.window_seconds, 1)
                return
            
            if int(current_requests) >= self.max_requests:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Rate limit exceeded"
                )
            
            await redis.incr(key)
            
        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # 如果Redis出错，允许请求通过
            pass


# 创建不同级别的限流器
standard_rate_limit = RateLimiter(max_requests=100, window_seconds=60)
strict_rate_limit = RateLimiter(max_requests=10, window_seconds=60)
upload_rate_limit = RateLimiter(max_requests=5, window_seconds=60)
