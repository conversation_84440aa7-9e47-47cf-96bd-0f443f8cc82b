"""
数据模型
存储提取和处理的数据
"""

from sqlalchemy import Column, String, Text, JSON, DateTime, Float, ForeignKey, Enum, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum as PyEnum
from typing import Optional, Dict, Any

from app.models.base import BaseModel


class DataType(PyEnum):
    """数据类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    TABLE = "table"
    FORM = "form"
    LINK = "link"
    DOCUMENT = "document"
    SCREENSHOT = "screenshot"
    OTHER = "other"


class DataStatus(PyEnum):
    """数据状态枚举"""
    RAW = "raw"  # 原始数据
    PROCESSING = "processing"  # 处理中
    PROCESSED = "processed"  # 已处理
    FAILED = "failed"  # 处理失败
    ARCHIVED = "archived"  # 已归档


class Data(BaseModel):
    """数据模型"""
    __tablename__ = "data"

    # 基础信息
    name = Column(String(200), nullable=False)
    description = Column(Text)
    data_type = Column(Enum(DataType), nullable=False, index=True)
    status = Column(Enum(DataStatus), default=DataStatus.RAW, index=True)
    
    # 关联信息
    user_id = Column(String, ForeignKey("users.id"), nullable=False, index=True)
    task_id = Column(String, ForeignKey("tasks.id"), nullable=True, index=True)
    
    # 数据内容
    raw_data = Column(JSON)  # 原始数据
    processed_data = Column(JSON)  # 处理后的数据
    metadata = Column(JSON)  # 元数据
    
    # 文件信息
    file_path = Column(String(500))  # 文件路径
    file_size = Column(Integer)  # 文件大小（字节）
    file_type = Column(String(50))  # 文件类型
    checksum = Column(String(64))  # 文件校验和
    
    # 处理信息
    processing_time = Column(Float)  # 处理时间（秒）
    error_message = Column(Text)  # 错误信息
    
    # 统计信息
    access_count = Column(Integer, default=0)  # 访问次数
    last_accessed = Column(DateTime)  # 最后访问时间
    
    # 关系
    user = relationship("User", back_populates="data")
    task = relationship("Task", back_populates="data")

    def __repr__(self):
        return f"<Data(id='{self.id}', name='{self.name}', type='{self.data_type}')>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "data_type": self.data_type.value,
            "status": self.status.value,
            "user_id": self.user_id,
            "task_id": self.task_id,
            "raw_data": self.raw_data,
            "processed_data": self.processed_data,
            "metadata": self.metadata,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "processing_time": self.processing_time,
            "error_message": self.error_message,
            "access_count": self.access_count,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    def mark_as_processing(self):
        """标记为处理中"""
        self.status = DataStatus.PROCESSING
        self.updated_at = datetime.utcnow()

    def mark_as_processed(self, processing_time: float = None):
        """标记为已处理"""
        self.status = DataStatus.PROCESSED
        if processing_time:
            self.processing_time = processing_time
        self.updated_at = datetime.utcnow()

    def mark_as_failed(self, error_message: str):
        """标记为处理失败"""
        self.status = DataStatus.FAILED
        self.error_message = error_message
        self.updated_at = datetime.utcnow()

    def increment_access(self):
        """增加访问次数"""
        self.access_count += 1
        self.last_accessed = datetime.utcnow()
        self.updated_at = datetime.utcnow()
