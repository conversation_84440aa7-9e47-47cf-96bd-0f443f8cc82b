"""
WebSocket实时通信接口
"""

from typing import Dict, List
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from fastapi.websockets import WebSocketState
import json
import asyncio
from datetime import datetime

from app.core.dependencies import verify_token
from app.models.user import User
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self.user_connections: Dict[str, str] = {}  # websocket_id -> user_id

    async def connect(self, websocket: WebSocket, user_id: str):
        """建立连接"""
        await websocket.accept()

        if user_id not in self.active_connections:
            self.active_connections[user_id] = []

        self.active_connections[user_id].append(websocket)
        self.user_connections[id(websocket)] = user_id

        logger.info(f"WebSocket connected for user: {user_id}")

    def disconnect(self, websocket: WebSocket):
        """断开连接"""
        websocket_id = id(websocket)
        if websocket_id in self.user_connections:
            user_id = self.user_connections[websocket_id]

            if user_id in self.active_connections:
                self.active_connections[user_id].remove(websocket)
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]

            del self.user_connections[websocket_id]
            logger.info(f"WebSocket disconnected for user: {user_id}")

    async def send_personal_message(self, message: dict, user_id: str):
        """发送个人消息"""
        if user_id in self.active_connections:
            disconnected_connections = []

            for connection in self.active_connections[user_id]:
                try:
                    if connection.client_state == WebSocketState.CONNECTED:
                        await connection.send_text(json.dumps(message))
                    else:
                        disconnected_connections.append(connection)
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {e}")
                    disconnected_connections.append(connection)

            # 清理断开的连接
            for conn in disconnected_connections:
                self.disconnect(conn)

    async def broadcast(self, message: dict):
        """广播消息"""
        for user_id, connections in self.active_connections.items():
            await self.send_personal_message(message, user_id)

    def get_user_count(self) -> int:
        """获取在线用户数"""
        return len(self.active_connections)

    def get_connection_count(self) -> int:
        """获取连接总数"""
        return sum(len(connections) for connections in self.active_connections.values())


# 全局连接管理器
manager = ConnectionManager()


@router.websocket("/connect")
async def websocket_endpoint(websocket: WebSocket, token: str = None):
    """WebSocket连接端点"""
    if not token:
        await websocket.close(code=4001, reason="Missing token")
        return

    try:
        # 验证令牌
        token_data = verify_token(token)
        user_id = token_data.user_id

        # 建立连接
        await manager.connect(websocket, user_id)

        # 发送连接成功消息
        await websocket.send_text(
            json.dumps(
                {
                    "type": "connection_established",
                    "user_id": user_id,
                    "timestamp": datetime.utcnow().isoformat(),
                }
            )
        )

        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)

                # 处理不同类型的消息
                await handle_websocket_message(websocket, user_id, message)

        except WebSocketDisconnect:
            manager.disconnect(websocket)
            logger.info(f"WebSocket disconnected for user: {user_id}")

    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close(code=4000, reason="Authentication failed")


async def handle_websocket_message(websocket: WebSocket, user_id: str, message: dict):
    """处理WebSocket消息"""
    message_type = message.get("type")

    try:
        if message_type == "ping":
            # 心跳检测
            await websocket.send_text(
                json.dumps({"type": "pong", "timestamp": datetime.utcnow().isoformat()})
            )

        elif message_type == "task_status_request":
            # 任务状态请求
            task_id = message.get("task_id")
            if task_id:
                # 这里可以查询任务状态并返回
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "task_status_response",
                            "task_id": task_id,
                            "status": "running",  # 实际应该从数据库查询
                            "timestamp": datetime.utcnow().isoformat(),
                        }
                    )
                )

        elif message_type == "subscribe_task":
            # 订阅任务更新
            task_id = message.get("task_id")
            if task_id:
                # 这里可以实现任务订阅逻辑
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "subscription_confirmed",
                            "task_id": task_id,
                            "timestamp": datetime.utcnow().isoformat(),
                        }
                    )
                )

        else:
            # 未知消息类型
            await websocket.send_text(
                json.dumps(
                    {
                        "type": "error",
                        "message": f"Unknown message type: {message_type}",
                        "timestamp": datetime.utcnow().isoformat(),
                    }
                )
            )

    except Exception as e:
        logger.error(f"Error handling WebSocket message: {e}")
        await websocket.send_text(
            json.dumps(
                {
                    "type": "error",
                    "message": "Internal server error",
                    "timestamp": datetime.utcnow().isoformat(),
                }
            )
        )


# 用于其他服务调用的消息发送函数
async def send_task_update(
    user_id: str, task_id: str, status: str, progress: float = None
):
    """发送任务更新消息"""
    message = {
        "type": "task_update",
        "task_id": task_id,
        "status": status,
        "timestamp": datetime.utcnow().isoformat(),
    }

    if progress is not None:
        message["progress"] = progress

    await manager.send_personal_message(message, user_id)


async def send_notification(
    user_id: str, title: str, content: str, level: str = "info"
):
    """发送通知消息"""
    message = {
        "type": "notification",
        "title": title,
        "content": content,
        "level": level,
        "timestamp": datetime.utcnow().isoformat(),
    }

    await manager.send_personal_message(message, user_id)


async def send_system_message(message_content: str):
    """发送系统广播消息"""
    message = {
        "type": "system_message",
        "content": message_content,
        "timestamp": datetime.utcnow().isoformat(),
    }

    await manager.broadcast(message)


@router.get("/stats")
async def get_websocket_stats():
    """获取WebSocket统计信息"""
    return {
        "online_users": manager.get_user_count(),
        "total_connections": manager.get_connection_count(),
        "timestamp": datetime.utcnow().isoformat(),
    }
