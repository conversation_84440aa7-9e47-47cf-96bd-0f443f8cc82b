"""
认证服务
处理用户认证和授权逻辑
"""

from typing import Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.models.user import User
from app.core.security import verify_password, get_password_hash, create_tokens
from app.utils.logger import get_logger

logger = get_logger(__name__)


class AuthService:
    """认证服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """验证用户凭据"""
        try:
            user = self.db.query(User).filter(
                (User.username == username) | (User.email == username)
            ).first()
            
            if not user:
                return None
            
            if not verify_password(password, user.hashed_password):
                return None
            
            if not user.is_active:
                return None
            
            # 更新最后登录时间
            user.last_login = datetime.utcnow()
            self.db.commit()
            
            return user
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    async def create_user(self, username: str, email: str, password: str, full_name: str = None) -> Optional[User]:
        """创建新用户"""
        try:
            # 检查用户名是否已存在
            if self.db.query(User).filter(User.username == username).first():
                return None
            
            # 检查邮箱是否已存在
            if self.db.query(User).filter(User.email == email).first():
                return None
            
            # 创建用户
            user = User(
                username=username,
                email=email,
                full_name=full_name,
                hashed_password=get_password_hash(password),
                is_active=True,
                created_at=datetime.utcnow()
            )
            
            self.db.add(user)
            self.db.commit()
            self.db.refresh(user)
            
            logger.info(f"User created: {username}")
            return user
            
        except Exception as e:
            logger.error(f"User creation error: {e}")
            self.db.rollback()
            return None
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """根据ID获取用户"""
        try:
            return self.db.query(User).filter(User.id == user_id).first()
        except Exception as e:
            logger.error(f"Get user by ID error: {e}")
            return None
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            return self.db.query(User).filter(User.username == username).first()
        except Exception as e:
            logger.error(f"Get user by username error: {e}")
            return None
    
    async def update_user_password(self, user_id: str, new_password: str) -> bool:
        """更新用户密码"""
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                return False
            
            user.hashed_password = get_password_hash(new_password)
            user.updated_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"Password updated for user: {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Update password error: {e}")
            self.db.rollback()
            return False
    
    async def deactivate_user(self, user_id: str) -> bool:
        """停用用户"""
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                return False
            
            user.is_active = False
            user.updated_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"User deactivated: {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Deactivate user error: {e}")
            self.db.rollback()
            return False
