"""
消息队列工具
基于Redis实现的简单消息队列
"""

import asyncio
import json
from typing import Any, Dict, Callable, Optional
from datetime import datetime

from app.utils.redis_client import get_redis_client
from app.utils.logger import get_logger

logger = get_logger(__name__)


class MessageQueue:
    """消息队列类"""
    
    def __init__(self, queue_prefix: str = "mq"):
        self.queue_prefix = queue_prefix
        self._subscribers: Dict[str, Callable] = {}
        self._running = False
    
    async def publish(self, topic: str, message: Dict[str, Any]) -> bool:
        """发布消息"""
        try:
            redis_client = await get_redis_client()
            
            # 添加时间戳和消息ID
            message_data = {
                "id": f"{datetime.utcnow().timestamp()}",
                "timestamp": datetime.utcnow().isoformat(),
                "topic": topic,
                "data": message
            }
            
            queue_key = f"{self.queue_prefix}:{topic}"
            message_json = json.dumps(message_data, ensure_ascii=False)
            
            # 推送到队列
            await redis_client.lpush(queue_key, message_json)
            
            logger.debug(f"Published message to {topic}: {message}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish message to {topic}: {e}")
            return False
    
    async def subscribe(self, topic: str, handler: Callable[[Dict[str, Any]], None]):
        """订阅主题"""
        self._subscribers[topic] = handler
        logger.info(f"Subscribed to topic: {topic}")
    
    async def unsubscribe(self, topic: str):
        """取消订阅"""
        if topic in self._subscribers:
            del self._subscribers[topic]
            logger.info(f"Unsubscribed from topic: {topic}")
    
    async def start_consumer(self):
        """启动消费者"""
        if self._running:
            return
        
        self._running = True
        logger.info("Message queue consumer started")
        
        while self._running:
            try:
                await self._consume_messages()
                await asyncio.sleep(0.1)  # 短暂休眠避免CPU占用过高
            except Exception as e:
                logger.error(f"Consumer error: {e}")
                await asyncio.sleep(1)
    
    async def stop_consumer(self):
        """停止消费者"""
        self._running = False
        logger.info("Message queue consumer stopped")
    
    async def _consume_messages(self):
        """消费消息"""
        redis_client = await get_redis_client()
        
        for topic in self._subscribers.keys():
            queue_key = f"{self.queue_prefix}:{topic}"
            
            try:
                # 从队列右端弹出消息
                message_json = await redis_client.rpop(queue_key)
                
                if message_json:
                    message_data = json.loads(message_json)
                    handler = self._subscribers[topic]
                    
                    # 异步调用处理器
                    if asyncio.iscoroutinefunction(handler):
                        await handler(message_data["data"])
                    else:
                        handler(message_data["data"])
                    
                    logger.debug(f"Processed message from {topic}")
                    
            except Exception as e:
                logger.error(f"Error processing message from {topic}: {e}")
    
    async def get_queue_length(self, topic: str) -> int:
        """获取队列长度"""
        try:
            redis_client = await get_redis_client()
            queue_key = f"{self.queue_prefix}:{topic}"
            return await redis_client.llen(queue_key)
        except Exception as e:
            logger.error(f"Error getting queue length for {topic}: {e}")
            return 0
    
    async def clear_queue(self, topic: str) -> bool:
        """清空队列"""
        try:
            redis_client = await get_redis_client()
            queue_key = f"{self.queue_prefix}:{topic}"
            await redis_client.delete(queue_key)
            logger.info(f"Cleared queue: {topic}")
            return True
        except Exception as e:
            logger.error(f"Error clearing queue {topic}: {e}")
            return False


class TaskMessageQueue(MessageQueue):
    """任务消息队列"""
    
    def __init__(self):
        super().__init__("task_mq")
    
    async def publish_task_created(self, task_id: str, user_id: str, config: Dict[str, Any]):
        """发布任务创建消息"""
        await self.publish("task.created", {
            "task_id": task_id,
            "user_id": user_id,
            "config": config
        })
    
    async def publish_task_started(self, task_id: str, user_id: str):
        """发布任务开始消息"""
        await self.publish("task.started", {
            "task_id": task_id,
            "user_id": user_id
        })
    
    async def publish_task_progress(self, task_id: str, progress: float, message: str = None):
        """发布任务进度消息"""
        await self.publish("task.progress", {
            "task_id": task_id,
            "progress": progress,
            "message": message
        })
    
    async def publish_task_completed(self, task_id: str, status: str, result: Dict[str, Any] = None):
        """发布任务完成消息"""
        await self.publish("task.completed", {
            "task_id": task_id,
            "status": status,
            "result": result
        })
    
    async def publish_task_failed(self, task_id: str, error: str):
        """发布任务失败消息"""
        await self.publish("task.failed", {
            "task_id": task_id,
            "error": error
        })


# 全局消息队列实例
_message_queue: Optional[MessageQueue] = None
_task_message_queue: Optional[TaskMessageQueue] = None


async def get_message_queue() -> MessageQueue:
    """获取消息队列实例"""
    global _message_queue
    
    if _message_queue is None:
        _message_queue = MessageQueue()
    
    return _message_queue


async def get_task_message_queue() -> TaskMessageQueue:
    """获取任务消息队列实例"""
    global _task_message_queue
    
    if _task_message_queue is None:
        _task_message_queue = TaskMessageQueue()
    
    return _task_message_queue
