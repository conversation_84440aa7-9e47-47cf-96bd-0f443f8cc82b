"""
通知服务
处理消息推送、邮件通知等功能
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio

from app.utils.logger import get_logger
from app.utils.redis_client import get_redis_client
from app.api.v1.websocket import send_notification, send_task_update

logger = get_logger(__name__)


class NotificationService:
    """通知服务类"""
    
    def __init__(self):
        self.email_config = {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "username": "",  # 从环境变量获取
            "password": "",  # 从环境变量获取
        }
    
    async def send_task_notification(
        self, 
        user_id: str, 
        task_id: str, 
        event_type: str, 
        message: str,
        data: Optional[Dict[str, Any]] = None
    ):
        """发送任务相关通知"""
        try:
            # WebSocket实时通知
            await send_task_update(user_id, task_id, event_type, data.get("progress") if data else None)
            
            # 系统通知
            await send_notification(
                user_id=user_id,
                title=f"任务{event_type}",
                content=message,
                level="info"
            )
            
            # 记录通知历史
            await self._save_notification_history(user_id, "task", message, data)
            
            logger.info(f"Task notification sent: {event_type} for task {task_id}")
            
        except Exception as e:
            logger.error(f"Send task notification error: {e}")
    
    async def send_system_notification(
        self, 
        user_id: str, 
        title: str, 
        content: str, 
        level: str = "info",
        persistent: bool = False
    ):
        """发送系统通知"""
        try:
            # WebSocket通知
            await send_notification(user_id, title, content, level)
            
            # 持久化通知
            if persistent:
                await self._save_notification_history(user_id, "system", content, {
                    "title": title,
                    "level": level
                })
            
            logger.info(f"System notification sent to user {user_id}: {title}")
            
        except Exception as e:
            logger.error(f"Send system notification error: {e}")
    
    async def send_email_notification(
        self, 
        to_email: str, 
        subject: str, 
        content: str,
        html_content: Optional[str] = None
    ) -> bool:
        """发送邮件通知"""
        try:
            if not self.email_config["username"] or not self.email_config["password"]:
                logger.warning("Email configuration not set")
                return False
            
            # 创建邮件
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = self.email_config["username"]
            msg["To"] = to_email
            
            # 添加文本内容
            text_part = MIMEText(content, "plain", "utf-8")
            msg.attach(text_part)
            
            # 添加HTML内容
            if html_content:
                html_part = MIMEText(html_content, "html", "utf-8")
                msg.attach(html_part)
            
            # 发送邮件
            with smtplib.SMTP(self.email_config["smtp_server"], self.email_config["smtp_port"]) as server:
                server.starttls()
                server.login(self.email_config["username"], self.email_config["password"])
                server.send_message(msg)
            
            logger.info(f"Email sent to {to_email}: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Send email error: {e}")
            return False
    
    async def send_batch_notification(
        self, 
        user_ids: List[str], 
        title: str, 
        content: str,
        level: str = "info"
    ):
        """批量发送通知"""
        try:
            tasks = []
            for user_id in user_ids:
                task = self.send_system_notification(user_id, title, content, level)
                tasks.append(task)
            
            # 并发发送
            await asyncio.gather(*tasks, return_exceptions=True)
            
            logger.info(f"Batch notification sent to {len(user_ids)} users")
            
        except Exception as e:
            logger.error(f"Send batch notification error: {e}")
    
    async def get_user_notifications(
        self, 
        user_id: str, 
        page: int = 1, 
        size: int = 20
    ) -> Dict[str, Any]:
        """获取用户通知历史"""
        try:
            redis_client = await get_redis_client()
            
            # 从Redis获取通知历史
            key = f"notifications:{user_id}"
            notifications = await redis_client.lrange(key, (page - 1) * size, page * size - 1)
            
            # 解析通知数据
            result = []
            for notification_json in notifications:
                try:
                    notification = await redis_client.get_json(notification_json)
                    result.append(notification)
                except Exception:
                    continue
            
            total = await redis_client.llen(key)
            
            return {
                "notifications": result,
                "total": total,
                "page": page,
                "size": size
            }
            
        except Exception as e:
            logger.error(f"Get user notifications error: {e}")
            return {"notifications": [], "total": 0, "page": page, "size": size}
    
    async def mark_notification_read(self, user_id: str, notification_id: str) -> bool:
        """标记通知为已读"""
        try:
            redis_client = await get_redis_client()
            
            # 更新通知状态
            key = f"notification:{notification_id}"
            notification = await redis_client.get_json(key)
            
            if notification and notification.get("user_id") == user_id:
                notification["read"] = True
                notification["read_at"] = datetime.utcnow().isoformat()
                await redis_client.set_json(key, notification, ex=86400 * 30)  # 30天过期
                
                logger.info(f"Notification marked as read: {notification_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Mark notification read error: {e}")
            return False
    
    async def clear_user_notifications(self, user_id: str) -> bool:
        """清空用户通知"""
        try:
            redis_client = await get_redis_client()
            
            key = f"notifications:{user_id}"
            await redis_client.delete(key)
            
            logger.info(f"User notifications cleared: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Clear user notifications error: {e}")
            return False
    
    async def _save_notification_history(
        self, 
        user_id: str, 
        notification_type: str, 
        content: str,
        data: Optional[Dict[str, Any]] = None
    ):
        """保存通知历史"""
        try:
            redis_client = await get_redis_client()
            
            notification = {
                "id": f"notif_{datetime.utcnow().timestamp()}",
                "user_id": user_id,
                "type": notification_type,
                "content": content,
                "data": data or {},
                "read": False,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # 保存通知详情
            await redis_client.set_json(
                f"notification:{notification['id']}", 
                notification, 
                ex=86400 * 30  # 30天过期
            )
            
            # 添加到用户通知列表
            await redis_client.lpush(
                f"notifications:{user_id}", 
                notification["id"]
            )
            
            # 限制通知数量（保留最新100条）
            await redis_client.ltrim(f"notifications:{user_id}", 0, 99)
            
        except Exception as e:
            logger.error(f"Save notification history error: {e}")
    
    def configure_email(self, smtp_server: str, smtp_port: int, username: str, password: str):
        """配置邮件服务"""
        self.email_config.update({
            "smtp_server": smtp_server,
            "smtp_port": smtp_port,
            "username": username,
            "password": password
        })
        logger.info("Email configuration updated")
